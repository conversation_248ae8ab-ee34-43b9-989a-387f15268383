# 🏢 Продакшен настройка с Reverse Proxy

## 🎯 Архитектура

```
Интернет → nginx (80/443) → Docker nginx (8080/8443) → <PERSON><PERSON> (8000)
```

**Преимущества:**
- ✅ Не конфликтует с существующими сайтами
- ✅ Можно использовать один SSL сертификат для всех сайтов
- ✅ Централизованное логирование и мониторинг
- ✅ Rate limiting на уровне хоста

## 🔧 Настройка системного nginx

### 1. Создать конфигурацию для бота

```bash
sudo nano /etc/nginx/sites-available/telebot
```

```nginx
# Конфигурация для Telegram бота
server {
    listen 80;
    server_name bot.yourdomain.com;  # Замените на ваш домен
    
    # Редирект на HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name bot.yourdomain.com;  # Замените на ваш домен
    
    # SSL сертификаты (используйте существующие или создайте новые)
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    # SSL настройки
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Логи
    access_log /var/log/nginx/telebot_access.log;
    error_log /var/log/nginx/telebot_error.log;
    
    # Проксирование на Docker nginx
    location / {
        proxy_pass http://127.0.0.1:8443;  # HTTPS к Docker nginx
        proxy_ssl_verify off;  # Отключить проверку SSL для внутреннего соединения
        
        # Заголовки
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Таймауты
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Health check
    location /health {
        proxy_pass http://127.0.0.1:8080/health;  # HTTP для health check
        proxy_set_header Host $host;
        access_log off;  # Не логировать health checks
    }
}
```

### 2. Активировать конфигурацию

```bash
# Включить сайт
sudo ln -s /etc/nginx/sites-available/telebot /etc/nginx/sites-enabled/

# Проверить конфигурацию
sudo nginx -t

# Перезагрузить nginx
sudo systemctl reload nginx
```

## 🔒 SSL сертификаты

### Вариант 1: Использовать существующие сертификаты

Если у вас уже есть SSL для основного домена:

```nginx
# Использовать wildcard сертификат
ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
```

### Вариант 2: Создать отдельный сертификат для бота

```bash
# Остановить Docker nginx временно
docker-compose stop nginx

# Создать сертификат для поддомена
sudo certbot certonly --nginx -d bot.yourdomain.com

# Запустить Docker nginx обратно
docker-compose start nginx
```

## 🚀 Запуск и проверка

### 1. Запустить бота

```bash
# Запустить бота на портах 8080/8443
docker-compose up -d

# Проверить что контейнеры работают
docker-compose ps
```

### 2. Проверить доступность

```bash
# Проверить внутренние порты
curl -I http://localhost:8080/health
curl -I -k https://localhost:8443/health

# Проверить внешний доступ
curl -I https://bot.yourdomain.com/health
```

### 3. Настроить webhook

```bash
# Установить webhook URL
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://bot.yourdomain.com/webhook"}'
```

## 📊 Мониторинг

### Логи nginx

```bash
# Логи доступа
sudo tail -f /var/log/nginx/telebot_access.log

# Логи ошибок
sudo tail -f /var/log/nginx/telebot_error.log
```

### Логи Docker

```bash
# Логи бота
docker-compose logs -f bot

# Логи nginx
docker-compose logs -f nginx
```

## 🛡️ Безопасность

### Rate limiting

Добавить в nginx конфигурацию:

```nginx
# В http блок /etc/nginx/nginx.conf
http {
    limit_req_zone $binary_remote_addr zone=telebot:10m rate=10r/s;
    
    # В server блок
    server {
        location /webhook {
            limit_req zone=telebot burst=20 nodelay;
            proxy_pass http://127.0.0.1:8443;
        }
    }
}
```

### Firewall

```bash
# Закрыть прямой доступ к Docker портам
sudo ufw deny 8080
sudo ufw deny 8443

# Разрешить только nginx
sudo ufw allow 80
sudo ufw allow 443
```

## 🔧 Альтернативная схема (только HTTP внутри)

Если хотите SSL только на уровне хоста:

```nginx
# Проксировать на HTTP порт Docker
location / {
    proxy_pass http://127.0.0.1:8080;  # Только HTTP
}
```

Тогда в docker-compose.yml:

```yaml
nginx:
  ports:
    - "8080:80"  # Только HTTP порт
```

## 💡 Рекомендации

1. **Используйте поддомен** для бота (bot.domain.com)
2. **Настройте мониторинг** логов и метрик
3. **Регулярно обновляйте** SSL сертификаты
4. **Тестируйте** webhook после каждого изменения
5. **Делайте бэкапы** конфигураций nginx

**Результат:** Безопасный продакшен деплой без конфликтов портов!
