#!/bin/bash

# Скрипт настройки автоматических бэкапов PostgreSQL
# Использование: ./setup_backups.sh

set -e

echo "🔧 Настройка автоматических бэкапов PostgreSQL..."

# Создаем директорию для бэкапов
BACKUP_DIR="/var/backups/telebot"
sudo mkdir -p $BACKUP_DIR
sudo chmod 755 $BACKUP_DIR

echo "📁 Создана директория бэкапов: $BACKUP_DIR"

# Создаем скрипт бэкапа
BACKUP_SCRIPT="/usr/local/bin/telebot_backup.sh"

sudo tee $BACKUP_SCRIPT > /dev/null << 'EOF'
#!/bin/bash

# Скрипт автоматического бэкапа PostgreSQL для телеграм-бота
# Запускается через cron

set -e

# Настройки
BACKUP_DIR="/var/backups/telebot"
PROJECT_DIR="/root/telebot"  # Путь к проекту с docker-compose.yml
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/telebot_backup_$DATE.sql"
LOG_FILE="/var/log/telebot_backup.log"

# Функция логирования
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a $LOG_FILE
}

log "🔄 Начало создания бэкапа..."

# Переходим в директорию проекта
cd $PROJECT_DIR

# Проверяем, что контейнер PostgreSQL запущен
if ! docker-compose ps postgres | grep -q "Up"; then
    log "❌ ОШИБКА: Контейнер PostgreSQL не запущен"
    exit 1
fi

# Создаем бэкап
if docker-compose exec -T postgres pg_dump -U telebot_user telebot > $BACKUP_FILE; then
    log "✅ Бэкап создан: $BACKUP_FILE"
    
    # Проверяем размер файла
    SIZE=$(du -h $BACKUP_FILE | cut -f1)
    log "📊 Размер бэкапа: $SIZE"
    
    # Сжимаем бэкап
    gzip $BACKUP_FILE
    log "🗜️ Бэкап сжат: $BACKUP_FILE.gz"
    
else
    log "❌ ОШИБКА: Не удалось создать бэкап"
    exit 1
fi

# Удаляем старые бэкапы (старше 7 дней)
DELETED=$(find $BACKUP_DIR -name "telebot_backup_*.sql.gz" -mtime +7 -delete -print | wc -l)
if [ $DELETED -gt 0 ]; then
    log "🗑️ Удалено старых бэкапов: $DELETED"
fi

# Проверяем общий размер бэкапов
TOTAL_SIZE=$(du -sh $BACKUP_DIR | cut -f1)
log "💾 Общий размер бэкапов: $TOTAL_SIZE"

log "✅ Бэкап завершен успешно"

# Отправляем уведомление в Telegram (опционально)
# Раскомментируйте и настройте, если нужны уведомления
# BOT_TOKEN="your_bot_token"
# CHAT_ID="your_chat_id"
# MESSAGE="✅ Бэкап телеграм-бота создан успешно%0AРазмер: $SIZE%0AВремя: $(date)"
# curl -s "https://api.telegram.org/bot$BOT_TOKEN/sendMessage?chat_id=$CHAT_ID&text=$MESSAGE" > /dev/null

EOF

# Делаем скрипт исполняемым
sudo chmod +x $BACKUP_SCRIPT

echo "📝 Создан скрипт бэкапа: $BACKUP_SCRIPT"

# Добавляем задачу в cron (каждый день в 3:00)
CRON_JOB="0 3 * * * $BACKUP_SCRIPT"

# Проверяем, есть ли уже такая задача
if ! crontab -l 2>/dev/null | grep -q "$BACKUP_SCRIPT"; then
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    echo "⏰ Добавлена cron-задача: ежедневно в 3:00"
else
    echo "⏰ Cron-задача уже существует"
fi

# Создаем файл логов
sudo touch /var/log/telebot_backup.log
sudo chmod 644 /var/log/telebot_backup.log

echo ""
echo "✅ Автобэкапы настроены успешно!"
echo ""
echo "📋 Информация:"
echo "   • Бэкапы сохраняются в: $BACKUP_DIR"
echo "   • Расписание: каждый день в 3:00"
echo "   • Хранятся: 7 дней"
echo "   • Логи: /var/log/telebot_backup.log"
echo ""
echo "🧪 Тестирование:"
echo "   sudo $BACKUP_SCRIPT  # Запустить бэкап вручную"
echo "   tail -f /var/log/telebot_backup.log  # Смотреть логи"
echo ""
echo "📱 Для уведомлений в Telegram:"
echo "   Отредактируйте $BACKUP_SCRIPT"
echo "   Раскомментируйте и настройте секцию с BOT_TOKEN"
