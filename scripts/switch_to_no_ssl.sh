#!/bin/bash

# Скрипт для переключения на конфигурацию без SSL

echo "🔄 Переключаемся на HTTP режим (без SSL)..."

# Загружаем переменные окружения для получения домена
if [ -f "/etc/edu_telebot/env" ]; then
    source /etc/edu_telebot/env
elif [ -f ".env" ]; then
    source .env
else
    echo "⚠️ Файл с переменными окружения не найден, используем localhost"
    DOMAIN="localhost"
fi

# Останавливаем контейнеры
echo "🛑 Останавливаем контейнеры..."
sudo docker-compose down

# Создаем резервную копию текущей конфигурации
if [ -f "nginx/nginx.conf" ] && [ ! -f "nginx/nginx.conf.ssl-backup" ]; then
    echo "💾 Создаем резервную копию SSL конфигурации..."
    cp nginx/nginx.conf nginx/nginx.conf.ssl-backup
fi

# Копируем конфигурацию без SSL
echo "📝 Устанавливаем конфигурацию без SSL..."
cp nginx/nginx-no-ssl.conf nginx/nginx.conf

# Обновляем переменные окружения для HTTP режима
echo "⚙️ Обновляем переменные окружения..."
if [ -f "/etc/edu_telebot/env" ]; then
    sudo sed -i 's/WEBHOOK_MODE=true/WEBHOOK_MODE=false/' /etc/edu_telebot/env
    sudo sed -i 's|WEBHOOK_HOST=https://|WEBHOOK_HOST=http://|' /etc/edu_telebot/env
fi

echo "✅ Конфигурация обновлена"
echo ""
echo "🚀 Запускаем контейнеры..."
sudo docker-compose up -d

echo ""
echo "✅ Готово! Бот работает в HTTP режиме"
echo "🌐 Webhook URL: http://${DOMAIN}/webhook"
echo ""
echo "💡 Для включения SSL позже запустите:"
echo "   ./scripts/ssl_manager.sh"
