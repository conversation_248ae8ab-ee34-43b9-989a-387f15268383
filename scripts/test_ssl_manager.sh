#!/bin/bash

# Тест SSL менеджера - проверяем что всё работает правильно

echo "🧪 Тестирование SSL менеджера"
echo "============================="

# Проверяем что SSL менеджер существует и исполняемый
if [ ! -f "scripts/ssl_manager.sh" ]; then
    echo "❌ SSL менеджер не найден!"
    exit 1
fi

if [ ! -x "scripts/ssl_manager.sh" ]; then
    echo "🔧 Делаем SSL менеджер исполняемым..."
    chmod +x scripts/ssl_manager.sh
fi

echo "✅ SSL менеджер найден и исполняемый"

# Проверяем nginx конфигурации
echo ""
echo "📋 Проверка nginx конфигураций:"

if [ ! -f "nginx/nginx.conf" ]; then
    echo "❌ nginx/nginx.conf не найден!"
    exit 1
fi

if [ ! -f "nginx/nginx-no-ssl.conf" ]; then
    echo "❌ nginx/nginx-no-ssl.conf не найден!"
    exit 1
fi

echo "✅ Nginx конфигурации найдены"

# Проверяем что в nginx-no-ssl.conf нет хардкода домена
if grep -q "n70741z2.beget.tech" nginx/nginx-no-ssl.conf; then
    echo "⚠️ В nginx-no-ssl.conf найден хардкод домена Beget"
    echo "Исправляем..."
    sed -i 's/n70741z2.beget.tech/${DOMAIN}/g' nginx/nginx-no-ssl.conf
    echo "✅ Хардкод домена исправлен"
else
    echo "✅ Хардкод домена отсутствует"
fi

# Проверяем что в nginx.conf есть переменная DOMAIN
if grep -q '${DOMAIN}' nginx/nginx.conf; then
    echo "✅ nginx.conf использует переменную DOMAIN"
else
    echo "⚠️ nginx.conf не использует переменную DOMAIN"
fi

# Проверяем переменные окружения
echo ""
echo "⚙️ Проверка переменных окружения:"

if [ -f "/etc/edu_telebot/env" ]; then
    echo "✅ /etc/edu_telebot/env найден"
    
    # Проверяем основные переменные
    if grep -q "DOMAIN=" /etc/edu_telebot/env; then
        DOMAIN_VALUE=$(grep "DOMAIN=" /etc/edu_telebot/env | cut -d'=' -f2)
        echo "DOMAIN: $DOMAIN_VALUE"
    else
        echo "⚠️ DOMAIN не установлен"
    fi
    
    if grep -q "WEBHOOK_MODE=" /etc/edu_telebot/env; then
        WEBHOOK_MODE_VALUE=$(grep "WEBHOOK_MODE=" /etc/edu_telebot/env | cut -d'=' -f2)
        echo "WEBHOOK_MODE: $WEBHOOK_MODE_VALUE"
    else
        echo "⚠️ WEBHOOK_MODE не установлен"
    fi
    
elif [ -f ".env" ]; then
    echo "✅ .env найден (режим разработки)"
else
    echo "⚠️ Файл переменных окружения не найден"
fi

# Проверяем Docker
echo ""
echo "🐳 Проверка Docker:"

if command -v docker &> /dev/null; then
    echo "✅ Docker установлен: $(docker --version | head -1)"
else
    echo "❌ Docker не установлен"
fi

if command -v docker-compose &> /dev/null; then
    echo "✅ Docker Compose установлен: $(docker-compose --version | head -1)"
else
    echo "❌ Docker Compose не установлен"
fi

# Проверяем что старые SSL скрипты удалены
echo ""
echo "🗑️ Проверка удаления старых скриптов:"

OLD_SCRIPTS=("scripts/setup_ssl.sh" "scripts/setup_ssl_acme.sh" "scripts/switch_to_no_ssl.sh")
ALL_REMOVED=true

for script in "${OLD_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        echo "⚠️ Старый скрипт найден: $script"
        ALL_REMOVED=false
    fi
done

if $ALL_REMOVED; then
    echo "✅ Все старые SSL скрипты удалены"
fi

# Проверяем синтаксис SSL менеджера
echo ""
echo "🔍 Проверка синтаксиса SSL менеджера:"

if bash -n scripts/ssl_manager.sh; then
    echo "✅ Синтаксис SSL менеджера корректен"
else
    echo "❌ Ошибка синтаксиса в SSL менеджере!"
    exit 1
fi

# Финальный отчет
echo ""
echo "📊 Финальный отчет:"
echo "=================="

if $ALL_REMOVED; then
    echo "✅ Старые скрипты удалены"
else
    echo "⚠️ Найдены старые скрипты"
fi

echo "✅ SSL менеджер готов к использованию"
echo "✅ Nginx конфигурации проверены"
echo "✅ Переменные окружения проверены"

echo ""
echo "🚀 Для использования SSL менеджера:"
echo "   ./scripts/ssl_manager.sh"
echo ""
echo "💡 Функции SSL менеджера:"
echo "   1) Показать статус - полная диагностика"
echo "   2) Включить HTTPS - автоматически создаст SSL"
echo "   3) Включить HTTP - для тестирования"
echo "   4) Создать SSL сертификаты - только создание"
echo "   5) Выход"

echo ""
echo "🎉 Тестирование завершено успешно!"
