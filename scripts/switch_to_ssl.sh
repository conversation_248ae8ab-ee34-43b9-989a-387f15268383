#!/bin/bash

# Скрипт для переключения на конфигурацию с SSL

echo "🔒 Переключаемся на HTTPS режим (с SSL)..."

# Загружаем переменные окружения
if [ -f "/etc/edu_telebot/env" ]; then
    source /etc/edu_telebot/env
elif [ -f ".env" ]; then
    source .env
else
    echo "❌ Файл с переменными окружения не найден!"
    exit 1
fi

# Проверяем домен
if [ -z "$DOMAIN" ] || [ "$DOMAIN" = "localhost" ] || [ "$DOMAIN" = "your-domain.com" ]; then
    echo "❌ Для SSL нужен настоящий домен!"
    echo "Текущий домен: ${DOMAIN:-не установлен}"
    echo ""
    echo "💡 Настройте домен в файле переменных:"
    echo "   sudo nano /etc/edu_telebot/env"
    echo "   Установите: DOMAIN=ваш-домен.com"
    exit 1
fi

# Проверяем наличие SSL сертификатов
if [ ! -f "nginx/ssl/fullchain.pem" ] || [ ! -f "nginx/ssl/privkey.pem" ]; then
    echo "❌ SSL сертификаты не найдены!"
    echo "Сначала создайте сертификаты:"
    echo "   ./scripts/ssl_manager.sh"
    echo "   Выберите пункт 4 (Создать новые SSL сертификаты)"
    exit 1
fi

echo "✅ SSL сертификаты найдены"
echo "🌐 Домен: $DOMAIN"

# Останавливаем контейнеры
echo "🛑 Останавливаем контейнеры..."
sudo docker-compose down

# Восстанавливаем SSL конфигурацию
if [ -f "nginx/nginx.conf.ssl-backup" ]; then
    echo "📝 Восстанавливаем SSL конфигурацию из резервной копии..."
    cp nginx/nginx.conf.ssl-backup nginx/nginx.conf
else
    echo "📝 Используем стандартную SSL конфигурацию..."
    # Проверяем, есть ли SSL конфигурация в основном файле
    if ! grep -q "listen 443 ssl" nginx/nginx.conf; then
        echo "⚠️ В nginx.conf нет SSL конфигурации"
        echo "💡 Используйте SSL менеджер для настройки: ./scripts/ssl_manager.sh"
        exit 1
    fi
fi

# Обновляем server_name в конфигурации
echo "🔧 Обновляем домен в nginx конфигурации..."
sed -i "s/server_name \${DOMAIN};/server_name $DOMAIN;/g" nginx/nginx.conf
sed -i "s/server_name _;/server_name $DOMAIN;/g" nginx/nginx.conf

# Обновляем переменные окружения для HTTPS режима
echo "⚙️ Обновляем переменные окружения..."
if [ -f "/etc/edu_telebot/env" ]; then
    sudo sed -i 's/WEBHOOK_MODE=false/WEBHOOK_MODE=true/' /etc/edu_telebot/env
    sudo sed -i 's|WEBHOOK_HOST=http://|WEBHOOK_HOST=https://|' /etc/edu_telebot/env
    sudo sed -i "s|DOMAIN=.*|DOMAIN=$DOMAIN|" /etc/edu_telebot/env
fi

# Проверяем права доступа к сертификатам
echo "🔐 Проверяем права доступа к SSL сертификатам..."
chmod 644 nginx/ssl/fullchain.pem 2>/dev/null || true
chmod 600 nginx/ssl/privkey.pem 2>/dev/null || true

echo "✅ Конфигурация обновлена"
echo ""
echo "🚀 Запускаем контейнеры..."
sudo docker-compose up -d

# Ждем запуска nginx
echo "⏳ Ждем запуска nginx..."
sleep 5

# Проверяем статус
if sudo docker-compose ps nginx | grep -q "Up"; then
    echo "✅ Nginx запущен успешно"
else
    echo "❌ Ошибка запуска nginx, проверьте логи:"
    echo "   sudo docker-compose logs nginx"
    exit 1
fi

echo ""
echo "🎉 Готово! Бот работает в HTTPS режиме"
echo "🌐 Webhook URL: https://${DOMAIN}/webhook"
echo ""
echo "🧪 Тестирование:"
echo "   curl -I https://${DOMAIN}/health"
echo ""
echo "💡 Для отключения SSL запустите:"
echo "   ./scripts/switch_to_no_ssl.sh"
