# 🛡️ Чеклист безопасного деплоя на продакшен сервер

## ⚠️ КРИТИЧЕСКИЕ ПРОВЕРКИ (ОБЯЗАТЕЛЬНО!)

### 1. 🔌 Проверка портов
```bash
# ПЕРЕД деплоем проверьте что порты свободны:
sudo netstat -tlnp | grep -E ":80|:443"

# Если порты заняты:
# - Остановите существующий nginx/apache
# - Или измените порты в docker-compose.yml
```

**РИСК:** Бот заблокирует существующие сайты!

### 2. 🌐 Проверка nginx
```bash
# Проверьте есть ли системный nginx:
sudo systemctl status nginx
sudo systemctl status apache2

# Если есть - остановите или измените порты бота
```

### 3. 🐳 Проверка Docker
```bash
# Проверьте что Docker не сломает другие контейнеры:
docker ps
docker network ls

# Убедитесь что нет конфликтов имен контейнеров
```

### 4. 💾 Бэкап ПЕРЕД деплоем
```bash
# Обязательные бэкапы:
sudo cp -r /etc/nginx /etc/nginx.backup.$(date +%Y%m%d)
sudo cp /etc/systemd/system/*.service /tmp/services.backup.$(date +%Y%m%d)/ 2>/dev/null || true
docker ps > docker_containers.backup.$(date +%Y%m%d).txt
```

## 🔧 БЕЗОПАСНАЯ НАСТРОЙКА

### Шаг 1: Изменить порты (если нужно)
```yaml
# В docker-compose.yml измените порты:
nginx:
  ports:
    - "8080:80"    # Вместо 80
    - "8443:443"   # Вместо 443
```

### Шаг 2: Проверить переменные окружения
```bash
# Убедитесь что нет конфликтов:
env | grep -E "DOMAIN|WEBHOOK|BOT_TOKEN"
```

### Шаг 3: Тестовый запуск
```bash
# Сначала запустите без автозапуска:
./deploy.sh
# Выберите пункт 1 (быстрый запуск)
# НЕ настраивайте автозапуск сразу!
```

### Шаг 4: Проверка работы
```bash
# Убедитесь что другие сервисы работают:
curl -I http://localhost        # Ваш сайт
curl -I http://localhost:8080   # Бот (если изменили порт)
```

## 🚨 ЧТО МОЖЕТ ПОСТРАДАТЬ

### КРИТИЧНО:
- ✅ **Веб-сайты** (порты 80/443)
- ✅ **Другие Docker контейнеры** (конфликт имен)
- ✅ **Systemd сервисы** (конфликт автозапуска)

### СРЕДНЕ:
- ⚠️ **Права пользователей** (группа docker)
- ⚠️ **Переменные окружения** (конфликт имен)

### НИЗКО:
- 🟢 **Системные файлы** (бот изолирован)
- 🟢 **Другие приложения** (Docker изоляция)

## ✅ БЕЗОПАСНЫЕ ДЕЙСТВИЯ

### Что НЕ ОПАСНО:
1. **Просмотр статуса** - `./scripts/ssl_manager.sh` (пункт 1)
2. **Тестовый запуск** - `./deploy.sh` (пункт 1)
3. **Проверка логов** - `docker-compose logs`

### Что ТРЕБУЕТ ОСТОРОЖНОСТИ:
1. **Автозапуск** - может конфликтовать с другими сервисами
2. **SSL настройка** - может занять порты 80/443
3. **Полная пересборка** - удалит все Docker данные

## 🛠️ ПЛАН БЕЗОПАСНОГО ДЕПЛОЯ

### 1. Подготовка (5 мин)
```bash
# Проверяем порты
sudo netstat -tlnp | grep -E ":80|:443"

# Проверяем сервисы
sudo systemctl status nginx apache2 2>/dev/null || true

# Делаем бэкапы
sudo cp -r /etc/nginx /etc/nginx.backup.$(date +%Y%m%d) 2>/dev/null || true
```

### 2. Настройка портов (2 мин)
```bash
# Если порты заняты - измените в docker-compose.yml:
# 80:80 → 8080:80
# 443:443 → 8443:443
```

### 3. Тестовый деплой (5 мин)
```bash
./deploy.sh
# Выберите: 1 (быстрый запуск)
# НЕ настраивайте автозапуск!
```

### 4. Проверка (3 мин)
```bash
# Проверяем что всё работает:
curl -I http://localhost        # Старые сайты
curl -I http://localhost:8080   # Новый бот
docker-compose ps               # Статус контейнеров
```

### 5. Финализация (2 мин)
```bash
# Только если всё работает:
# - Настройте автозапуск
# - Настройте SSL
# - Настройте мониторинг
```

## 🆘 ПЛАН ВОССТАНОВЛЕНИЯ

Если что-то сломалось:

```bash
# 1. Остановить бота
docker-compose down

# 2. Восстановить nginx
sudo systemctl start nginx

# 3. Восстановить бэкапы
sudo cp -r /etc/nginx.backup.* /etc/nginx

# 4. Перезапустить сервисы
sudo systemctl restart nginx
```

## 💡 РЕКОМЕНДАЦИИ

1. **Тестируйте на копии сервера** сначала
2. **Используйте отдельные порты** для бота
3. **Не запускайте автозапуск** сразу
4. **Делайте бэкапы** перед каждым изменением
5. **Мониторьте логи** после деплоя

**Главное правило: лучше потратить 10 минут на проверки, чем час на восстановление!**
