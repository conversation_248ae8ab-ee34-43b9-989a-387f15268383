# SSL настройка для Beget VPS

## 🎯 Краткое описание

На Beget VPS у вас есть несколько вариантов работы с SSL:

1. **HTTP режим** - простой, без SSL (для тестирования)
2. **HTTPS режим** - с собственными SSL сертификатами через Let's Encrypt
3. **Beget SSL** - использование встроенных SSL сертификатов Beget (если доступно)

## 🚀 Быстрый старт

### Вариант 1: HTTP режим (рекомендуется для начала)

```bash
# Переключиться на HTTP режим
chmod +x scripts/switch_to_no_ssl.sh
./scripts/switch_to_no_ssl.sh
```

### Вариант 2: HTTPS режим с собственными сертификатами

```bash
# Использовать SSL менеджер
chmod +x scripts/ssl_manager.sh
./scripts/ssl_manager.sh
```

## 🔧 SSL Менеджер

Новый упрощенный инструмент для управления SSL:

```bash
./scripts/ssl_manager.sh
```

**Возможности:**
- Показать текущий статус SSL
- Включить/выключить HTTPS режим
- Создать новые SSL сертификаты
- Переключиться между HTTP и HTTPS

## 📁 Структура SSL файлов

```
nginx/
├── nginx.conf              # Конфигурация с SSL
├── nginx-no-ssl.conf       # Конфигурация без SSL
└── ssl/                    # SSL сертификаты
    ├── fullchain.pem       # Полная цепочка сертификатов
    ├── privkey.pem         # Приватный ключ
    └── cert.pem            # Основной сертификат
```

## ⚙️ Переменные окружения

В файле `/etc/edu_telebot/env`:

```bash
# Для HTTP режима
WEBHOOK_MODE=false
WEBHOOK_HOST=http://your-domain.com
DOMAIN=your-domain.com

# Для HTTPS режима
WEBHOOK_MODE=true
WEBHOOK_HOST=https://your-domain.com
DOMAIN=your-domain.com
```

## 🔍 Диагностика проблем

### Проверка статуса SSL

```bash
# Через SSL менеджер
./scripts/ssl_manager.sh

# Вручную
openssl x509 -in nginx/ssl/fullchain.pem -noout -dates
```

### Проверка nginx конфигурации

```bash
# Проверить синтаксис
sudo docker-compose exec nginx nginx -t

# Перезагрузить nginx
sudo docker-compose restart nginx
```

### Проверка портов

```bash
# Проверить какие порты заняты
sudo netstat -tlnp | grep -E ":80|:443"

# Проверить статус контейнеров
sudo docker-compose ps
```

## 🚨 Частые проблемы

### 1. Порт 80 занят

**Проблема:** `bind: address already in use`

**Решение:**
```bash
# Найти процесс на порту 80
sudo netstat -tlnp | grep :80

# Остановить nginx
sudo docker-compose stop nginx

# Или убить процесс
sudo kill -9 PID
```

### 2. SSL сертификаты не найдены

**Проблема:** `ssl_certificate /etc/ssl/fullchain.pem not found`

**Решение:**
```bash
# Создать сертификаты
./scripts/ssl_manager.sh
# Выбрать пункт 4

# Или переключиться на HTTP
./scripts/switch_to_no_ssl.sh
```

### 3. Домен не указывает на сервер

**Проблема:** Let's Encrypt не может проверить домен

**Решение:**
1. Проверить DNS записи домена
2. Убедиться что домен указывает на IP сервера
3. Подождать распространения DNS (до 24 часов)

## 💡 Рекомендации для Beget

### Для начала работы:
1. Используйте HTTP режим для тестирования
2. Настройте домен и DNS
3. Переключитесь на HTTPS когда всё работает

### Для продакшена:
1. Используйте собственный домен (не поддомен Beget)
2. Настройте автообновление сертификатов
3. Регулярно проверяйте срок действия сертификатов

## 🔄 Автообновление сертификатов

acme.sh автоматически настраивает обновление сертификатов:

```bash
# Проверить задачи cron для acme.sh
crontab -l | grep acme

# Вручную обновить сертификаты
~/.acme.sh/acme.sh --renew-all
```

## 📞 Поддержка

Если возникли проблемы:

1. Проверьте логи: `sudo docker-compose logs nginx`
2. Используйте SSL менеджер: `./scripts/ssl_manager.sh`
3. Переключитесь на HTTP режим для отладки
4. Проверьте документацию Beget по SSL
